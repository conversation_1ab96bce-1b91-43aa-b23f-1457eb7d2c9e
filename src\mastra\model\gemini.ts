import { createOpenAI } from "@ai-sdk/openai";
import { createGoogleGenerativeAI } from "@ai-sdk/google";

export const gemini = createOpenAI({
  baseURL: "https://huandukeji.cn/v1",
  apiKey: "sk-873A6ezWLldS3FrsAXtoVQOTc9UVbBzeuyA1YRYsGkEgdRdT",
  compatibility: "compatible"
})("gemini-2.5-pro");

// export const gemini = createGoogleGenerativeAI({
//   apiKey: "AIzaSyAAmJa405DjZIcWi-e6Q-8IcoiQiDvAQk0"
// })("gemini-2.5-pro");
d