/**
 * GitHub Agent Evaluation Tests using Mastra Evals
 * 专门的GitHub代理测试，使用Mastra评估系统评估搜索质量和策略优化
 */

import { AnswerRelevancyMetric, PromptAlignmentMetric } from "@mastra/evals/llm";
import { ContentSimilarityMetric, CompletenessMetric } from "@mastra/evals/nlp";
import { openrouter } from "../model";
import { githubAgent } from "./github-agent";

// 评估模型配置
const evalModel = openrouter("anthropic/claude-sonnet-4");

// GitHub Agent搜索质量评估测试套件
class GitHubAgentEvaluator {
  async evaluateSearchStrategyOptimization() {
    console.log("🔍 评估GitHub搜索策略优化...");

    const query =
      "I need to find repositories for centralized environment variable management across multiple projects, not just .env files in each project";

    try {
      // 使用Answer Relevancy评估回答的相关性
      const relevancyMetric = new AnswerRelevancyMetric(evalModel);
      const response = await githubAgent.generate([{ role: "user", content: query }]);
      const result = await relevancyMetric.measure(query, response.text);

      console.log(`✅ Answer Relevancy Score: ${result.score.toFixed(3)}`);
      console.log(`📝 Reasoning: ${result.info?.reason || "No reasoning provided"}`);

      // 评估是否达到期望标准
      const passed = result.score > 0.8;
      console.log(
        `${passed ? "✅" : "❌"} Test ${passed ? "PASSED" : "FAILED"}: Expected score > 0.8, got ${result.score.toFixed(3)}`
      );

      return { passed, score: result.score, reasoning: result.info?.reason };
    } catch (error) {
      console.error("❌ 评估失败:", error);
      return { passed: false, score: 0, error: error.message };
    }
  }

  async evaluateAdvancedSearchSyntax() {
    console.log("\n🎯 评估高级搜索语法建议...");

    const query = "Find me Next.js 14 projects with TypeScript and good documentation";

    try {
      const response = await githubAgent.generate([{ role: "user", content: query }]);

      // 使用Prompt Alignment评估是否提供了高级搜索语法建议
      const instructions = [
        "Mention using in: qualifiers for targeted search",
        "Suggest exact matching for specific terms",
        "Recommend appropriate search parameters",
        "Explain search optimization strategies"
      ];

      const alignmentMetric = new PromptAlignmentMetric(evalModel, { instructions });
      const result = await alignmentMetric.measure(query, response.text);

      console.log(`✅ Prompt Alignment Score: ${result.score.toFixed(3)}`);
      console.log(`📝 Reasoning: ${result.info?.reason || "No reasoning provided"}`);

      const passed = result.score > 0.7;
      console.log(
        `${passed ? "✅" : "❌"} Test ${passed ? "PASSED" : "FAILED"}: Expected score > 0.7, got ${result.score.toFixed(3)}`
      );

      return { passed, score: result.score, reasoning: result.info?.reason };
    } catch (error) {
      console.error("❌ 评估失败:", error);
      return { passed: false, score: 0, error: error.message };
    }
  }

  async evaluateKeywordOptimization() {
    console.log("\n🔧 评估关键词优化能力...");

    const query =
      "Help me search for environment variable management tools for Node.js applications with production-ready features";

    try {
      const response = await githubAgent.generate([{ role: "user", content: query }]);

      // 使用Completeness评估是否覆盖了关键概念
      const completenessMetric = new CompletenessMetric();
      const result = await completenessMetric.measure(query, response.text);

      console.log(`✅ Completeness Score: ${result.score.toFixed(3)}`);
      console.log(`📊 Coverage Details:`, result.info);

      const passed = result.score > 0.8;
      console.log(
        `${passed ? "✅" : "❌"} Test ${passed ? "PASSED" : "FAILED"}: Expected score > 0.8, got ${result.score.toFixed(3)}`
      );

      return { passed, score: result.score, details: result.info };
    } catch (error) {
      console.error("❌ 评估失败:", error);
      return { passed: false, score: 0, error: error.message };
    }
  }

  async evaluateSearchQualityImprovement() {
    console.log("\n📈 评估搜索质量改进建议...");

    const query = "What advanced GitHub search techniques can improve my search results?";

    try {
      const response = await githubAgent.generate([{ role: "user", content: query }]);

      // 使用Answer Relevancy评估回答质量
      const relevancyMetric = new AnswerRelevancyMetric(evalModel);
      const result = await relevancyMetric.measure(query, response.text);

      console.log(`✅ Answer Relevancy Score: ${result.score.toFixed(3)}`);
      console.log(`📝 Reasoning: ${result.info?.reason || "No reasoning provided"}`);

      const passed = result.score > 0.85;
      console.log(
        `${passed ? "✅" : "❌"} Test ${passed ? "PASSED" : "FAILED"}: Expected score > 0.85, got ${result.score.toFixed(3)}`
      );

      return { passed, score: result.score, reasoning: result.info?.reason };
    } catch (error) {
      console.error("❌ 评估失败:", error);
      return { passed: false, score: 0, error: error.message };
    }
  }

  async evaluateExclusionStrategies() {
    console.log("\n🚫 评估排除策略建议...");

    const query = "I need production-ready authentication libraries, not tutorials or examples";

    try {
      const response = await githubAgent.generate([{ role: "user", content: query }]);

      // 检查是否提到了排除策略
      const instructions = [
        "Mention excluding tutorial or example repositories",
        "Suggest using excludeTerms parameter",
        "Recommend filtering for production-ready code",
        "Explain how to avoid irrelevant results"
      ];

      const alignmentMetric = new PromptAlignmentMetric(evalModel, { instructions });
      const result = await alignmentMetric.measure(query, response.text);

      console.log(`✅ Exclusion Strategy Score: ${result.score.toFixed(3)}`);
      console.log(`📝 Reasoning: ${result.info?.reason || "No reasoning provided"}`);

      const passed = result.score > 0.6;
      console.log(
        `${passed ? "✅" : "❌"} Test ${passed ? "PASSED" : "FAILED"}: Expected score > 0.6, got ${result.score.toFixed(3)}`
      );

      return { passed, score: result.score, reasoning: result.info?.reason };
    } catch (error) {
      console.error("❌ 评估失败:", error);
      return { passed: false, score: 0, error: error.message };
    }
  }

  async runAllEvaluations() {
    console.log("🧪 GitHub Agent 搜索质量评估测试套件");
    console.log("=====================================");

    const results = [];

    // 运行所有评估
    results.push(await this.evaluateSearchStrategyOptimization());
    results.push(await this.evaluateAdvancedSearchSyntax());
    results.push(await this.evaluateKeywordOptimization());
    results.push(await this.evaluateSearchQualityImprovement());
    results.push(await this.evaluateExclusionStrategies());

    // 计算总体结果
    const passedTests = results.filter(r => r.passed).length;
    const totalTests = results.length;
    const averageScore = results.reduce((sum, r) => sum + r.score, 0) / totalTests;

    console.log("\n📊 评估结果总结:");
    console.log(`✅ 通过测试: ${passedTests}/${totalTests}`);
    console.log(`📈 平均评分: ${averageScore.toFixed(3)}`);
    console.log(
      `🎯 总体评估: ${passedTests >= totalTests * 0.8 ? "优秀" : passedTests >= totalTests * 0.6 ? "良好" : "需要改进"}`
    );

    return {
      passedTests,
      totalTests,
      averageScore,
      results
    };
  }
}

// 运行评估
async function main() {
  const evaluator = new GitHubAgentEvaluator();
  const results = await evaluator.runAllEvaluations();

  console.log("\n🎉 评估完成!");

  // 如果有失败的测试，退出码为1
  if (results.passedTests < results.totalTests) {
    process.exit(1);
  }
}

// 直接运行评估
main().catch(console.error);

export { GitHubAgentEvaluator };
