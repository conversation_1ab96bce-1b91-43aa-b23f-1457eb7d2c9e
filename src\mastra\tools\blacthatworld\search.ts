import { retryFetch } from "../../proxies/index.js";
import { proxyManager } from "../../proxies/manager.js";
import FormData from "form-data";

interface NodeInfo {
  id: string;
  name: string;
  level: number;
  children: NodeInfo[];
  parent?: NodeInfo;
}

const rawNodes = [
  ["", "All forums"],
  ["23", "BlackHatWorld"],
  ["242", "   Newbie Guide"],
  ["261", "     Brand New to BHW"],
  ["262", "     Newbies"],
  ["263", "     Newbie+"],
  ["265", "     Marketplace Sellers"],
  ["299", "     New to Marketplace"],
  ["24", "   Introductions"],
  ["25", "   BlackHat Lounge"],
  ["26", "   Forum Suggestions & Feedback"],
  ["226", "     BHW Beta Testers"],
  ["31", "   Dispute Resolution"],
  ["303", "   Free Review Copies For Marketplace Approvals"],
  ["73", "The Marketplace"],
  ["203", "   BHW Marketplace rules and how to post"],
  ["310", "   Account Selling / Renting Services"],
  ["193", "   Affiliate programs - CPA networks"],
  ["194", "   Content / Copywriting"],
  ["195", "   Domains & websites for sale"],
  ["308", "   eBooks, methods and courses"],
  ["196", "   Hosting"],
  ["197", "   Hot Deals"],
  ["198", "   Images, logos & videos"],
  ["18", "   Misc"],
  ["112", "   Proxies For Sale"],
  ["43", "   SEO - Link building"],
  ["199", "   SEO - Other"],
  ["206", "   SEO - Packages"],
  ["200", "   Social Media"],
  ["302", "   Social Media - Panels"],
  ["201", "   Web Design"],
  ["1", "Black Hat SEO"],
  ["252", "   AI - Artificial Intelligence in Digital Marketing"],
  ["28", "   Black Hat SEO"],
  ["9", "   Black Hat SEO Tools"],
  ["3", "   Blogging"],
  ["2", "   Cloaking and Content Generators"],
  ["101", "   Proxies"],
  ["103", "     Proxy Lists"],
  ["280", "   Voice Search"],
  ["270", "Social Media"],
  ["32", "   General Social Chat"],
  ["86", "   FaceBook"],
  ["215", "   Instagram"],
  ["214", "   Linkedin"],
  ["87", "   Myspace"],
  ["211", "   Pinterest"],
  ["301", "   Reddit"],
  ["279", "   TikTok"],
  ["217", "   Tumblr"],
  ["216", "   Weibo"],
  ["210", "   X (formerly Twitter)"],
  ["77", "   YouTube"],
  ["95", "White Hat SEO"],
  ["168", "   Copywriting & Sales Persuasion"],
  ["173", "     Downloads - Copywriting & Sales"],
  ["53", "   Domain Names & Parking"],
  ["169", "   Graphic Design"],
  ["171", "     Downloads - Graphic design"],
  ["108", "   Link Building"],
  ["209", "   Local SEO"],
  ["224", "   Online Reputation Management (ORM)"],
  ["170", "   Video Production"],
  ["172", "     Downloads - Video production"],
  ["94", "   Web Hosting"],
  ["30", "   White Hat SEO"],
  ["11", "Making Money"],
  ["107", "   Associated Content & Writing Articles"],
  ["15", "   Affiliate Programs"],
  ["66", "     Clickbank"],
  ["71", "     CJ Affiliate"],
  ["72", "     Other Affiliate Programs"],
  ["225", "     Zero Parallel & T.UK"],
  ["96", "   Business & Tax Advice"],
  ["50", "   CPA"],
  ["218", "   CryptoCurrency"],
  ["68", "   Dropshipping & Wholesale Hookups"],
  ["69", "   Ebay"],
  ["76", "   Hire a Freelancer"],
  ["65", "   Joint Ventures"],
  ["12", "   Making Money"],
  ["175", "   Media Buying"],
  ["106", "   Membership Sites"],
  ["158", "   Mobile Marketing"],
  ["167", "   My Journey Discussions"],
  ["208", "   New Markets"],
  ["132", "   Offline Marketing"],
  ["13", "   Pay Per Click"],
  ["83", "     Google Ads"],
  ["219", "     Facebook"],
  ["93", "     Yahoo & Bing MSN"],
  ["85", "     Other PPC Networks"],
  ["125", "     General PPC Discussion"],
  ["205", "   Pay Per Install"],
  ["102", "   Pay Per View"],
  ["141", "   Site Flipping"],
  ["75", "   Torrents"],
  ["174", "   Freebies / Giveaways"],
  ["165", "   Service Reviews & Beta Testers Help Wanted"],
  ["40", "Programming & Web Design"],
  ["79", "   Programming"],
  ["128", "     General Programming Chat"],
  ["60", "     C, C++, C#"],
  ["61", "     Visual Basic 6"],
  ["62", "     Visual Basic .NET"],
  ["131", "     Other Languages"],
  ["59", "   Scripting"],
  ["129", "     General Scripting Chat"],
  ["80", "     HTML & JavaScript"],
  ["127", "     PHP & Perl"],
  ["130", "     Other Scripting Languages"],
  ["126", "   Web Design"],
  ["212", "Conferences / Events"],
  ["207", "   BHW Events"],
  ["221", "     UnGagged"],
  ["269", "       UnGagged Los Angeles"],
  ["235", "       UnGagged Las Vegas"],
  ["222", "       UnGagged London"],
  ["213", "       UnGagged 2014 - Las Vegas"]
];

// 构建节点树结构
function buildNodeTree(): { tree: NodeInfo[]; nameToIdMap: Map<string, string> } {
  const tree: NodeInfo[] = [];
  const nameToIdMap = new Map<string, string>();
  const nodeStack: NodeInfo[] = [];

  for (const [id, name] of rawNodes) {
    // 计算缩进级别（每两个空格为一级）
    const level = Math.floor((name.length - name.trimStart().length) / 2);
    const cleanName = name.trim();

    const node: NodeInfo = {
      id,
      name: cleanName,
      level,
      children: []
    };

    // 建立名称到ID的映射
    nameToIdMap.set(cleanName, id);

    // 根据级别确定父子关系
    while (nodeStack.length > 0 && nodeStack[nodeStack.length - 1].level >= level) {
      nodeStack.pop();
    }

    if (nodeStack.length === 0) {
      // 根节点
      tree.push(node);
    } else {
      // 子节点
      const parent = nodeStack[nodeStack.length - 1];
      node.parent = parent;
      parent.children.push(node);
    }

    nodeStack.push(node);
  }

  return { tree, nameToIdMap };
}

// 获取节点树的文字描述
function getNodesDescription(): string {
  const { tree } = buildNodeTree();

  function formatNode(node: NodeInfo, indent: string = ""): string {
    let result = `${indent}- ${node.name}`;
    if (node.id) {
      result += ` (ID: ${node.id})`;
    }
    result += "\n";

    for (const child of node.children) {
      result += formatNode(child, indent + "  ");
    }

    return result;
  }

  let description = "BlackHatWorld Forum Nodes Structure:\n\n";
  for (const rootNode of tree) {
    description += formatNode(rootNode);
  }

  return description;
}

// 根据节点名称获取节点ID
function getNodeIds(nodeNames: string[]): string[] {
  const { nameToIdMap } = buildNodeTree();
  const nodeIds: string[] = [];

  for (const name of nodeNames) {
    const id = nameToIdMap.get(name);
    if (id) {
      nodeIds.push(id);
    } else {
      console.warn(`Node not found: ${name}`);
    }
  }

  return nodeIds;
}

// 使用 form-data 库生成 multipart form data
function generateFormData(keywords: string, nodeIds: string[], newer_than: string, older_than: string, order: string): FormData {
  const form = new FormData();

  // 基础字段
  form.append("_xfToken", "1753754817,2c10c3b0b846c6f0d9a352b274ed80fb");
  form.append("keywords", keywords);
  form.append("c[users]", "");
  form.append("c[newer_than]", newer_than || "");
  form.append("c[older_than]", older_than || "");
  form.append("c[min_reply_count]", "0");

  // 如果有节点，添加child_nodes和节点列表
  if (nodeIds.length > 0) {
    form.append("c[child_nodes]", "1");
    nodeIds.forEach((nodeId, index) => {
      form.append(`c[nodes][${index}]`, nodeId);
    });
  }

  form.append("order", order || "relevance");
  form.append("search_type", "post");
  form.append("_xfResponseType", "json");
  form.append("_xfWithData", "1");
  form.append("_xfRequestUri", "/search/?type=post");
  form.append("_xfToken", "1753754817,2c10c3b0b846c6f0d9a352b274ed80fb");

  return form;
}

//  https://www.blackhatworld.com/search/22847739/?q=content+farm&t=post&c[child_nodes]=1&c[min_reply_count]=10&c[newer_than]=2025-01-14&c[older_than]=2025-07-27&c[nodes][0]=26&c[nodes][1]=226&c[nodes][2]=31&c[nodes][3]=303&c[nodes][4]=73&c[older_than]=2025-07-27&o=relevance&g=1
// 规则：1. 当 nodes 存在时存在child_nodes，older_than， newer_than  older_than 可为空， o 取值有 relevance  date  replies

/**
 * 搜索 BlackHatWorld 论坛
 * @param keywords 搜索关键词
 * @param nodeNames 节点名称数组（使用节点的显示名称，不是ID）
 * @param newer_than 开始日期 (YYYY-MM-DD)
 * @param older_than 结束日期 (YYYY-MM-DD)
 * @param order 排序方式: 'relevance' | 'date' | 'replies'
 * @returns Promise<string> 返回搜索结果的重定向URL
 */
async function search(
  keywords: string,
  nodeNames: string[] = [],
  newer_than: string = "",
  older_than: string = "",
  order: string = "relevance"
): Promise<string> {
  // 将节点名称转换为节点ID
  const nodeIds = getNodeIds(nodeNames);

  // 生成表单数据
  const formData = generateFormData(keywords, nodeIds, newer_than, older_than, order);

  const data: any = await retryFetch(dispatcher => {
    // 获取 form-data 的 headers（包含正确的 boundary）
    const formHeaders = formData.getHeaders();

    const optins = {
      // @ts-ignore
      dispatcher,
      headers: {
        ...formHeaders, // 包含正确的 content-type 和 boundary
        "content-type": "multipart/form-data; boundary=----WebKitFormBoundarySyD8ceA39bAgcl6b",
        accept: "application/json",
        "accept-language": "zh-CN,zh;q=0.9",
        priority: "u=1, i",
        "sec-ch-ua": '"Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"',
        "sec-ch-ua-arch": '"x86"',
        "sec-ch-ua-bitness": '"64"',
        "sec-ch-ua-full-version": '"140.0.7322.0"',
        "sec-ch-ua-full-version-list":
          '"Chromium";v="140.0.7322.0", "Not=A?Brand";v="********", "Google Chrome";v="140.0.7322.0"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-model": '""',
        "sec-ch-ua-platform": '"Windows"',
        "sec-ch-ua-platform-version": '"10.0.0"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "x-requested-with": "XMLHttpRequest",
        cookie:
          "_ga=GA1.1.968538537.1753585988; xf_notice_dismiss=-1; xf_csrf=-E-1gewjr4zp762E; xf_session=oadHVcJQ2luGfnxTYJDkfK73tBYcDMMc; _ga_VH2PZEKYEE=GS2.1.s1753945296$o14$g1$t1753945302$j54$l0$h0",
        Referer: "https://www.blackhatworld.com/search/?type=post"
      },
      // 使用 form-data 的 buffer 作为 body
      // body: formData.getBuffer().toString(),
      body: '------WebKitFormBoundarySyD8ceA39bAgcl6b\r\nContent-Disposition: form-data; name="_xfToken"\r\n\r\n1753945290,0c528ca97f1dfd01e19cc851e3797a6b\r\n------WebKitFormBoundarySyD8ceA39bAgcl6b\r\nContent-Disposition: form-data; name="keywords"\r\n\r\ncontent farm\r\n------WebKitFormBoundarySyD8ceA39bAgcl6b\r\nContent-Disposition: form-data; name="c[users]"\r\n\r\n\r\n------WebKitFormBoundarySyD8ceA39bAgcl6b\r\nContent-Disposition: form-data; name="c[newer_than]"\r\n\r\n\r\n------WebKitFormBoundarySyD8ceA39bAgcl6b\r\nContent-Disposition: form-data; name="c[older_than]"\r\n\r\n\r\n------WebKitFormBoundarySyD8ceA39bAgcl6b\r\nContent-Disposition: form-data; name="order"\r\n\r\nrelevance\r\n------WebKitFormBoundarySyD8ceA39bAgcl6b\r\nContent-Disposition: form-data; name="search_type"\r\n\r\n\r\n------WebKitFormBoundarySyD8ceA39bAgcl6b\r\nContent-Disposition: form-data; name="_xfResponseType"\r\n\r\njson\r\n------WebKitFormBoundarySyD8ceA39bAgcl6b\r\nContent-Disposition: form-data; name="_xfWithData"\r\n\r\n1\r\n------WebKitFormBoundarySyD8ceA39bAgcl6b\r\nContent-Disposition: form-data; name="_xfRequestUri"\r\n\r\n/search\r\n------WebKitFormBoundarySyD8ceA39bAgcl6b\r\nContent-Disposition: form-data; name="_xfToken"\r\n\r\n1753945290,0c528ca97f1dfd01e19cc851e3797a6b\r\n------WebKitFormBoundarySyD8ceA39bAgcl6b--\r\n',

      method: "POST"
    };

    console.log("optins：", optins);
    return fetch("https://www.blackhatworld.com/search/search", optins).then(res => res.json());
  });

  console.log("data：", data);

  return data.redirect;
}

// 导出函数供外部使用
export { search, getNodesDescription };

search("content farm");
