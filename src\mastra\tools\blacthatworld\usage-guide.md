# BlackHatWorld 工具使用指南

## 📋 工具概述

我们提供了两个专门的 BlackHatWorld 论坛工具，分别针对不同的使用场景：

### 1. `blackhatWorldSearchTool` - 内容搜索工具
**用途**：搜索特定主题和关键词的相关讨论
**适用场景**：当你有明确的搜索意图和关键词时使用

### 2. `blackhatWorldTrendingTool` - 热度浏览工具  
**用途**：浏览论坛最新热门讨论和趋势
**适用场景**：当你想了解某个版块的最新动态和热门话题时使用

## 🔍 工具对比

| 特性 | 搜索工具 | 热度浏览工具 |
|------|----------|--------------|
| **关键词** | 必需，具体搜索词 | 不需要，自动使用通配符 |
| **默认排序** | 相关性 (relevance) | 日期 (date) |
| **最小回复数** | 5 | 10 (更高门槛) |
| **默认时间范围** | 无限制 | 最近7天 |
| **主要用途** | 精确搜索 | 趋势发现 |

## 📝 使用示例

### 搜索工具示例

#### 1. 搜索联盟营销相关内容
```typescript
await blackhatWorldSearchTool.execute({
  context: {
    keywords: "affiliate marketing strategies",
    nodeNames: ["Making Money", "Affiliate Programs"],
    daysAgo: 30,
    order: "relevance",
    pages: 2,
    minReplies: 5
  }
});
```

#### 2. 搜索SEO工具讨论
```typescript
await blackhatWorldSearchTool.execute({
  context: {
    keywords: "SEO tools 2024",
    nodeNames: ["Black Hat SEO"],
    order: "replies", // 按回复数排序，找最热门的讨论
    pages: 1,
    minReplies: 10
  }
});
```

#### 3. 搜索特定技术话题
```typescript
await blackhatWorldSearchTool.execute({
  context: {
    keywords: "content farm automation",
    daysAgo: 7, // 最近一周
    order: "date", // 按时间排序，找最新的
    pages: 1
  }
});
```

### 热度浏览工具示例

#### 1. 浏览赚钱版块最新热门
```typescript
await blackhatWorldTrendingTool.execute({
  context: {
    nodeNames: ["Making Money"],
    daysAgo: 7, // 最近7天
    order: "date", // 按时间排序
    pages: 2,
    minReplies: 10 // 至少10个回复才算热门
  }
});
```

#### 2. 查看SEO版块最活跃讨论
```typescript
await blackhatWorldTrendingTool.execute({
  context: {
    nodeNames: ["Black Hat SEO", "White Hat SEO"],
    daysAgo: 14, // 最近两周
    order: "replies", // 按回复数排序，找最活跃的
    pages: 1,
    minReplies: 20 // 更高的回复门槛
  }
});
```

#### 3. 浏览多个版块的综合热度
```typescript
await blackhatWorldTrendingTool.execute({
  context: {
    nodeNames: ["Making Money", "Affiliate Programs", "CPA Marketing"],
    daysAgo: 3, // 最近3天
    order: "date",
    pages: 3
  }
});
```

## 🎯 选择指南

### 何时使用搜索工具？
- ✅ 你有具体的搜索关键词
- ✅ 想找特定主题的讨论
- ✅ 需要精确匹配的内容
- ✅ 想按相关性排序结果

**示例场景**：
- "我想找关于 YouTube 自动化的讨论"
- "搜索 ChatGPT 赚钱方法"
- "查找 dropshipping 成功案例"

### 何时使用热度浏览工具？
- ✅ 想了解某个版块的最新动态
- ✅ 发现热门趋势和话题
- ✅ 不确定具体搜索什么
- ✅ 想看最活跃的讨论

**示例场景**：
- "看看赚钱版块最近有什么热门话题"
- "了解SEO版块的最新趋势"
- "发现最近大家在讨论什么"

## 🔧 参数优化建议

### 搜索工具参数优化
- **关键词**：使用具体、相关的搜索词
- **排序**：relevance（相关性）适合大多数搜索
- **最小回复数**：5-10 适合一般搜索，15+ 适合找热门讨论
- **时间范围**：根据需要设置，新话题用较短时间

### 热度浏览工具参数优化
- **版块选择**：选择1-3个相关版块
- **排序**：date（最新）或 replies（最热）
- **最小回复数**：10-20 确保内容质量
- **时间范围**：7天适合日常浏览，14-30天适合趋势分析

## 💡 最佳实践

1. **先浏览后搜索**：使用热度浏览工具了解趋势，再用搜索工具深入特定话题
2. **组合使用**：两个工具互补，获得更全面的信息
3. **参数调优**：根据结果质量调整 minReplies 和时间范围
4. **版块选择**：选择最相关的版块，避免噪音
5. **分页控制**：从1页开始，根据需要增加页数

## 🚀 高级用法

### 趋势分析工作流
1. 使用热度浏览工具发现热门话题
2. 从结果中提取关键词
3. 使用搜索工具深入研究特定话题
4. 对比不同时间段的趋势变化

### 竞品研究工作流
1. 搜索竞品相关关键词
2. 浏览相关版块的最新讨论
3. 分析用户反馈和讨论重点
4. 发现市场机会和痛点
