import { ProxyAgent } from "undici";

const proxyAgent = new ProxyAgent("http://127.0.0.1:7890");

fetch("https://www.blackhatworld.com/search/search", {
  dispatcher: proxyAgent,
  headers: {
    accept: "application/json",
    "accept-language": "zh-CN,zh;q=0.9",
    "content-type": "multipart/form-data; boundary=----WebKitFormBoundarySyD8ceA39bAgcl6b",
    priority: "u=1, i",
    "sec-ch-ua": '"Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"',
    "sec-ch-ua-arch": '"x86"',
    "sec-ch-ua-bitness": '"64"',
    "sec-ch-ua-full-version": '"140.0.7326.0"',
    "sec-ch-ua-full-version-list": '"Chromium";v="140.0.7326.0", "Not=A?Brand";v="********", "Google Chrome";v="140.0.7326.0"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-model": '""',
    "sec-ch-ua-platform": '"Windows"',
    "sec-ch-ua-platform-version": '"10.0.0"',
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "x-requested-with": "XMLHttpRequest",
    cookie:
      "_ga=GA1.1.968538537.1753585988; xf_notice_dismiss=-1; xf_csrf=-E-1gewjr4zp762E; xf_session=oadHVcJQ2luGfnxTYJDkfK73tBYcDMMc; _ga_VH2PZEKYEE=GS2.1.s1753945296$o14$g1$t1753945302$j54$l0$h0",
    Referer: "https://www.blackhatworld.com/search"
  },
  body: '------WebKitFormBoundarySyD8ceA39bAgcl6b\r\nContent-Disposition: form-data; name="_xfToken"\r\n\r\n1753945290,0c528ca97f1dfd01e19cc851e3797a6b\r\n------WebKitFormBoundarySyD8ceA39bAgcl6b\r\nContent-Disposition: form-data; name="keywords"\r\n\r\ncontent farm\r\n------WebKitFormBoundarySyD8ceA39bAgcl6b\r\nContent-Disposition: form-data; name="c[users]"\r\n\r\n\r\n------WebKitFormBoundarySyD8ceA39bAgcl6b\r\nContent-Disposition: form-data; name="c[newer_than]"\r\n\r\n\r\n------WebKitFormBoundarySyD8ceA39bAgcl6b\r\nContent-Disposition: form-data; name="c[older_than]"\r\n\r\n\r\n------WebKitFormBoundarySyD8ceA39bAgcl6b\r\nContent-Disposition: form-data; name="order"\r\n\r\nrelevance\r\n------WebKitFormBoundarySyD8ceA39bAgcl6b\r\nContent-Disposition: form-data; name="search_type"\r\n\r\n\r\n------WebKitFormBoundarySyD8ceA39bAgcl6b\r\nContent-Disposition: form-data; name="_xfResponseType"\r\n\r\njson\r\n------WebKitFormBoundarySyD8ceA39bAgcl6b\r\nContent-Disposition: form-data; name="_xfWithData"\r\n\r\n1\r\n------WebKitFormBoundarySyD8ceA39bAgcl6b\r\nContent-Disposition: form-data; name="_xfRequestUri"\r\n\r\n/search\r\n------WebKitFormBoundarySyD8ceA39bAgcl6b\r\nContent-Disposition: form-data; name="_xfToken"\r\n\r\n1753945290,0c528ca97f1dfd01e19cc851e3797a6b\r\n------WebKitFormBoundarySyD8ceA39bAgcl6b--\r\n',
  method: "POST"
})
  .then(res => res.json())
  .then(console.log);
