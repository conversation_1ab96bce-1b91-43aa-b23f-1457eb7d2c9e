/**
 * GitHub API Client
 * Handles repository and code search operations
 */

import { request } from "undici";

export interface GitHubSearchParams {
  query: string;
  searchType: "repositories" | "code";
  user?: string;
  org?: string;
  repo?: string;
  language?: string;
  stars?: string;
  forks?: string;
  size?: string;
  createdDaysAgo?: number;
  pushedDaysAgo?: number;
  topics?: string[];
  license?: string;
  is?: string[];
  archived?: boolean;
  sort?: string;
  order?: "asc" | "desc";
  per_page?: number;
  page?: number;
  // 新增高级搜索参数
  in?: string[]; // 搜索范围：description, readme, name, topics
  exactMatch?: boolean; // 是否精准匹配
  logicOperator?: "AND" | "OR"; // 逻辑操作符
  excludeTerms?: string[]; // 排除的关键词
  orTerms?: string[]; // OR逻辑关键词
}

export interface GitHubRepository {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  html_url: string;
  clone_url: string;
  language: string | null;
  stargazers_count: number;
  forks_count: number;
  size: number;
  created_at: string;
  updated_at: string;
  pushed_at: string;
  topics: string[];
  license: string | null;
  owner: {
    login: string;
    type: string;
    avatar_url: string;
  };
}

export interface GitHubCodeResult {
  name: string;
  path: string;
  sha: string;
  url: string;
  git_url: string;
  html_url: string;
  repository: {
    id: number;
    name: string;
    full_name: string;
    html_url: string;
    description: string | null;
    language: string | null;
    stargazers_count: number;
    forks_count: number;
  };
  score: number;
}

export interface GitHubSearchResponse {
  total_count: number;
  incomplete_results: boolean;
  items: GitHubRepository[] | GitHubCodeResult[];
}

export class GitHubClient {
  private baseUrl = "https://api.github.com";

  /**
   * Build search query string for GitHub API with advanced search syntax
   */
  private buildQuery(params: GitHubSearchParams): string {
    const queryParts: string[] = [];
    const qualifiers: string[] = [];

    // 处理基础搜索词 - 支持精准匹配和智能分词
    if (params.query) {
      const searchTerms = this.processSearchTerms(params.query, params.exactMatch, params.in);
      if (searchTerms) {
        queryParts.push(searchTerms);
      }
    }

    // User/org/repo qualifiers
    if (params.user) qualifiers.push(`user:${params.user}`);
    if (params.org) qualifiers.push(`org:${params.org}`);
    if (params.repo) qualifiers.push(`repo:${params.repo}`);

    // Language
    if (params.language) qualifiers.push(`language:${params.language}`);

    // Numeric range searches
    if (params.stars) qualifiers.push(`stars:${params.stars}`);
    if (params.forks) qualifiers.push(`forks:${params.forks}`);
    if (params.size) qualifiers.push(`size:${params.size}`);

    // Date ranges - 转换 daysAgo 为具体日期
    if (params.createdDaysAgo !== undefined) {
      const createdDate = new Date();
      createdDate.setDate(createdDate.getDate() - params.createdDaysAgo);
      const createdDateStr = createdDate.toISOString().split("T")[0];
      qualifiers.push(`created:>${createdDateStr}`);
    }

    if (params.pushedDaysAgo !== undefined) {
      const pushedDate = new Date();
      pushedDate.setDate(pushedDate.getDate() - params.pushedDaysAgo);
      const pushedDateStr = pushedDate.toISOString().split("T")[0];
      qualifiers.push(`pushed:>${pushedDateStr}`);
    }

    // Topics (only for repository search)
    if (params.topics && params.searchType === "repositories") {
      params.topics.forEach(topic => qualifiers.push(`topic:${topic}`));
    }

    // License (only for repository search)
    if (params.license && params.searchType === "repositories") {
      qualifiers.push(`license:${params.license}`);
    }

    // Repository status (only for repository search)
    if (params.is && params.searchType === "repositories") {
      params.is.forEach(value => qualifiers.push(`is:${value}`));
    }

    // Archived status (only for repository search)
    if (params.archived !== undefined && params.searchType === "repositories") {
      qualifiers.push(`archived:${params.archived}`);
    }

    // 排除关键词
    if (params.excludeTerms) {
      params.excludeTerms.forEach(term => qualifiers.push(`-${term}`));
    }

    // OR逻辑关键词 - 支持 (term1 OR term2 OR term3) 语法
    if (params.orTerms && params.orTerms.length > 0) {
      if (params.orTerms.length === 1) {
        // 单个词直接添加
        queryParts.push(params.orTerms[0]);
      } else {
        // 多个词用括号和OR连接
        const orClause = `(${params.orTerms.join(" OR ")})`;
        queryParts.push(orClause);
      }
    }

    // 组合查询和限定符
    const finalQuery = [...queryParts, ...qualifiers].join(" ");

    console.log(`🔍 Built GitHub query: ${finalQuery}`);
    return finalQuery;
  }

  /**
   * 处理搜索词，支持精准匹配、in限定符和智能分词
   */
  private processSearchTerms(query: string, exactMatch?: boolean, inFields?: string[]): string {
    // 清理和分词
    const cleanQuery = query.trim();
    if (!cleanQuery) return "";

    // 智能分词 - 限制关键词数量，避免查询过长
    const words = cleanQuery.split(/\s+/).filter(word => word.length > 2 && !this.isStopWord(word.toLowerCase()));

    // 限制关键词数量，优先保留重要词汇
    const importantWords = this.prioritizeWords(words).slice(0, 4);

    let searchTerms: string;

    if (exactMatch && importantWords.length <= 2) {
      // 精准匹配 - 只对短查询使用
      searchTerms = `"${importantWords.join(" ")}"`;
    } else {
      // 智能组合关键词
      searchTerms = importantWords.join(" ");
    }

    // 添加 in 限定符
    if (inFields && inFields.length > 0) {
      const inClause = `in:${inFields.join(",")}`;
      return `${searchTerms} ${inClause}`;
    }

    return searchTerms;
  }

  /**
   * 判断是否为停用词
   */
  private isStopWord(word: string): boolean {
    const stopWords = [
      "the",
      "and",
      "or",
      "but",
      "in",
      "on",
      "at",
      "to",
      "for",
      "of",
      "with",
      "by",
      "from",
      "up",
      "about",
      "into",
      "through",
      "during",
      "before",
      "after",
      "above",
      "below",
      "between",
      "among",
      "is",
      "are",
      "was",
      "were",
      "be",
      "been",
      "being",
      "have",
      "has",
      "had",
      "do",
      "does",
      "did",
      "will",
      "would",
      "could",
      "should",
      "may",
      "might",
      "must",
      "can",
      "this",
      "that",
      "these",
      "those",
      "i",
      "you",
      "he",
      "she",
      "it",
      "we",
      "they",
      "me",
      "him",
      "her",
      "us",
      "them",
      "my",
      "your",
      "his",
      "its",
      "our",
      "their",
      "want",
      "need",
      "find",
      "search",
      "looking",
      "help"
    ];
    return stopWords.includes(word);
  }

  /**
   * 优先排序重要词汇
   */
  private prioritizeWords(words: string[]): string[] {
    // 技术关键词优先级更高
    const techKeywords = [
      "react",
      "vue",
      "angular",
      "node",
      "python",
      "java",
      "typescript",
      "javascript",
      "api",
      "auth",
      "database",
      "config",
      "environment",
      "docker",
      "kubernetes",
      "microservice",
      "serverless",
      "ai",
      "ml",
      "machine",
      "learning",
      "blockchain",
      "framework",
      "library",
      "tool",
      "cli",
      "sdk",
      "template",
      "starter",
      "boilerplate"
    ];

    return words.sort((a, b) => {
      const aIsTech = techKeywords.includes(a.toLowerCase());
      const bIsTech = techKeywords.includes(b.toLowerCase());

      if (aIsTech && !bIsTech) return -1;
      if (!aIsTech && bIsTech) return 1;
      return 0;
    });
  }

  /**
   * Get GitHub token
   */
  private getToken(): string {
    return (
      process.env.GITHUB_TOKEN || "*********************************************************************************************"
    );
  }

  /**
   * Execute search request
   */
  async search(params: GitHubSearchParams): Promise<GitHubSearchResponse> {
    const startTime = Date.now();

    try {
      console.log(`🔍 Searching GitHub ${params.searchType} for: ${params.query.substring(0, 50)}...`);

      const token = this.getToken();
      const query = this.buildQuery(params);

      if (!query.trim()) {
        throw new Error("Search query cannot be empty");
      }

      // Build URL parameters
      const urlParams = new URLSearchParams({
        q: query,
        sort: params.sort || "best-match",
        order: params.order || "desc",
        per_page: String(params.per_page || 30),
        page: String(params.page || 1)
      });

      const endpoint = params.searchType === "repositories" ? "repositories" : "code";
      const url = `${this.baseUrl}/search/${endpoint}?${urlParams}`;

      // Set headers
      const headers: Record<string, string> = {
        Accept: "application/vnd.github.v3+json",
        "User-Agent": "Mastra-GitHub-Search"
      };

      if (token) {
        headers["Authorization"] = `token ${token}`;
      }

      // Send request
      const { statusCode, body } = await request(url, {
        method: "GET",
        headers
      });

      if (statusCode !== 200) {
        throw new Error(`GitHub API request failed: ${statusCode}`);
      }

      const data = (await body.json()) as GitHubSearchResponse;
      const executionTime = Date.now() - startTime;

      console.log(`✅ GitHub search completed in ${executionTime}ms: ${data.total_count} results`);

      return data;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      console.error(`❌ GitHub search failed after ${executionTime}ms:`, error);
      throw new Error(`GitHub search failed: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}
