import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { search, getNodesDescription } from "./search";
import { scrapeArticle } from "./scraper";
import FireCrawlApp from "@mendable/firecrawl-js";
import * as cheerio from "cheerio";

const app = new FireCrawlApp({ apiKey: process.env.FIRECRAWL_API_KEY || "fc-dd20fa1c0ec4435a871e535d538b4b2f" });

// 搜索结果项接口
interface SearchResultItem {
  title: string;
  url: string;
  author: string;
  replies: number;
  views: number;
  lastPost: string;
  forum: string;
  snippet?: string;
}

// 搜索结果接口
interface SearchResults {
  searchUrl: string;
  results: SearchResultItem[];
  totalResults: number;
  currentPage: number;
  hasNextPage: boolean;
  maxPage: number;
}

/**
 * 解析搜索结果页面
 */
const parseSearchResults = async (searchUrl: string): Promise<SearchResults> => {
  try {
    const scrapeResult = await app.scrapeUrl(searchUrl, {
      formats: ["rawHtml"],
      onlyMainContent: false,
      parsePDF: false,
      maxAge: 14400000
    });

    if (!scrapeResult.success || !scrapeResult.rawHtml) {
      throw new Error("Failed to scrape search results");
    }

    const $ = cheerio.load(scrapeResult.rawHtml);
    const results: SearchResultItem[] = [];

    // 解析搜索结果项 - 基于新的HTML结构
    const searchItems = $("li.block-row");

    // 获取最大页数
    const maxPage = $(".pageNav-main li:last-child a").text();

    searchItems.each((index, element) => {
      const $item = $(element);

      // 获取标题和链接
      const titleElement = $item.find(".contentRow-title a").first();
      const title = titleElement.text().trim();
      const url = titleElement.attr("href");

      if (!title || !url) {
        return;
      }

      // 清理标题中的高亮标签 - 使用jQuery的text()方法自动清理HTML
      const cleanTitle = titleElement.clone().find("em").remove().end().text().trim();

      // 获取作者
      const author = $item.find(".contentRow-minor .username").first().text().trim();

      // 获取摘要 - 使用jQuery的text()方法自动清理HTML标签
      const snippetElement = $item.find(".contentRow-snippet");
      const snippet = snippetElement.clone().find("em").remove().end().text().trim();

      // 解析元数据列表
      const metaItems = $item.find(".contentRow-minor .listInline li");
      let replies = 0;
      let views = 0;
      let lastPost = "";
      let forum = "";
      let tags: string[] = [];

      metaItems.each((metaIndex, metaElement) => {
        const $meta = $(metaElement);
        const text = $meta.text().trim();

        if (metaIndex + 1 === 4) {
          tags = text?.replace(/\t/g, "").split("\n").filter(Boolean) || [];
        }

        // 解析回复数
        if (text.startsWith("Replies:")) {
          const repliesMatch = text.match(/Replies:\s*(\d+)/);
          if (repliesMatch) {
            replies = parseInt(repliesMatch[1]) || 0;
          }
        }

        // 解析浏览数 (如果存在)
        if (text.startsWith("Views:")) {
          const viewsMatch = text.match(/Views:\s*(\d+)/);
          if (viewsMatch) {
            views = parseInt(viewsMatch[1]) || 0;
          }
        }

        // 解析论坛
        if (text.startsWith("Forum:")) {
          const forumLink = $meta.find("a");
          forum = forumLink.length > 0 ? forumLink.text().trim() : text.replace("Forum:", "").trim();
        }

        // 解析时间
        const timeElement = $meta.find("time");
        if (timeElement.length > 0) {
          lastPost = timeElement.attr("title") || timeElement.text().trim();
        }
      });

      // 如果没有找到views，尝试其他方式获取或设置默认值
      if (views === 0) {
        // 可以根据回复数估算浏览数，或者设置默认值
        views = replies * 10; // 简单估算
      }

      const resultItem = {
        title: cleanTitle,
        url: url.startsWith("http") ? url : `https://www.blackhatworld.com${url}`,
        author,
        replies,
        views,
        lastPost,
        forum,
        snippet,
        tags
      };

      results.push(resultItem);
    });

    // 解析分页信息
    const totalResultsText = $(".block-header").text();
    const totalResults = parseInt(totalResultsText.match(/(\d+)/)?.[1] || "0");
    const currentPage = parseInt($(".pageNav-page--current").text()) || 1;
    const hasNextPage = $(".pageNav-jump--next").length > 0;

    return {
      searchUrl,
      results,
      totalResults,
      currentPage,
      hasNextPage,
      maxPage: parseInt(maxPage)
    };
  } catch (error) {
    console.error("Error parsing search results:", error);
    // 出错时返回空结果
    return {
      searchUrl,
      results: [],
      totalResults: 0,
      currentPage: 1,
      hasNextPage: false,
      maxPage: 1
    };
  }
};

const threads = getNodesDescription();

/**
 * 通用的 BlackHatWorld 搜索执行函数
 */
async function executeBlackhatWorldSearch(params: {
  keywords: string;
  nodeNames?: string[];
  daysAgo?: number;
  order?: "relevance" | "date" | "replies";
  pages?: number;
  minReplies?: number;
}) {
  const { keywords, pages = 1, nodeNames, daysAgo, order = "relevance", minReplies = 5 } = params;

  try {
    // 根据 daysAgo 计算日期范围
    let newerThan = "";
    let olderThan = "";

    if (daysAgo && daysAgo > 0) {
      const now = new Date();
      const startDate = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000);

      // 格式化为 YYYY-MM-DD
      newerThan = startDate.toISOString().split("T")[0];
      olderThan = now.toISOString().split("T")[0];
    }

    // 执行搜索获取重定向URL
    const searchUrl = await search(keywords, nodeNames, newerThan, olderThan, minReplies, order);

    // 解析搜索结果
    const searchResults = await parseSearchResults(searchUrl);

    // 获取额外页面的结果
    const moreSearchResults = await Promise.all(
      Array.from({ length: pages - 1 }, async (_, index) => {
        const page = index + 2;
        const pageUrl = `${searchUrl}&page=${page}`;
        const pageResults = await parseSearchResults(pageUrl);
        return pageResults.results;
      })
    );

    return {
      searchUrl: searchResults.searchUrl,
      results: [...searchResults.results, ...moreSearchResults.flat()],
      maxPage: searchResults.maxPage,
      totalResults: searchResults.totalResults
    };
  } catch (error) {
    console.error("BlackHatWorld search failed:", error);
    return {
      searchUrl: "",
      results: [],
      totalResults: 0,
      maxPage: 1
    };
  }
}

/**
 * BlackHatWorld 内容搜索工具 - 用于搜索特定主题和关键词
 */
export const blackhatWorldSearchTool = createTool({
  id: "blackhatworld-search",
  description:
    "Search BlackHatWorld forum for specific topics and discussions using keywords. Use this when you have specific search terms or topics to find relevant posts and discussions.",
  inputSchema: z.object({
    keywords: z.string().describe("Search keywords for specific topics (required for targeted search)"),
    nodeNames: z
      .array(z.string())
      .optional()
      .describe(`Forum node names to search in (e.g., ["Making Money", "Black Hat SEO"])\n\n${threads}`),
    daysAgo: z.number().optional().describe("Number of days ago to search from (e.g., 7 for last 7 days, 30 for last 30 days)"),
    order: z
      .enum(["relevance", "date", "replies"])
      .optional()
      .default("relevance")
      .describe("Sort order - relevance is best for keyword searches"),
    pages: z.number().optional().default(1).describe("Number of pages to scrape, 1 page = 20 results"),
    minReplies: z.number().optional().default(5).describe("Minimum number of replies to include in the results")
  }),
  outputSchema: z.object({
    searchUrl: z.string(),
    results: z
      .array(
        z.object({
          title: z.string(),
          url: z.string(),
          author: z.string(),
          replies: z.number(),
          views: z.number(),
          lastPost: z.string(),
          forum: z.string(),
          snippet: z.string().optional(),
          tags: z.array(z.string()).optional()
        })
      )
      .optional(),
    totalResults: z.number().optional(),
    maxPage: z.number().optional()
  }),
  execute: async ({ context }) => {
    const { keywords, pages, nodeNames, daysAgo, order, minReplies } = context;

    return await executeBlackhatWorldSearch({
      keywords,
      nodeNames,
      daysAgo,
      order,
      pages,
      minReplies
    });
  }
});

/**
 * BlackHatWorld 文章抓取工具
 */
export const blackhatWorldScrapeTool = createTool({
  id: "blackhatworld-scrape",
  description: "Scrape article content from BlackHatWorld forum posts",
  inputSchema: z.object({
    url: z.string().describe("URL of the forum post to scrape")
    // allReplies: z.boolean().optional().default(false).describe("Whether to scrape all replies")
  }),
  outputSchema: z.object({
    url: z.string(),
    title: z.string(),
    content: z.string(),
    author: z.string(),
    postDate: z.string(),
    replies: z.number(),
    views: z.number(),
    posts: z
      .array(
        z.object({
          postNumber: z.number(),
          author: z.string(),
          userTitle: z.string(),
          content: z.string(),
          date: z.string().nullable(),
          likes: z.number(),
          isMainPost: z.boolean()
        })
      )
      .optional(),
    totalPosts: z.number().optional(),
    mainPost: z
      .object({
        postNumber: z.number(),
        author: z.string(),
        userTitle: z.string(),
        content: z.string(),
        date: z.string().nullable(),
        likes: z.number(),
        isMainPost: z.boolean().describe("Whether the post is the main post")
      })
      .nullable()
      .optional(),
    threadId: z.string().nullable().optional(),
    forum: z.string().optional(),
    tags: z.array(z.string()).optional(),
    extractedAt: z.string().optional(),
    success: z.boolean()
  }),
  execute: async ({ context }) => {
    const { url } = context;

    try {
      const article = await scrapeArticle(url);

      if (!article) {
        return {
          url,
          title: "",
          content: "",
          author: "",
          postDate: "",
          replies: 0,
          views: 0,
          success: false
        };
      }

      return {
        ...article,
        success: true
      };
    } catch (error) {
      console.error("Article scraping error:", error);
      return {
        url,
        title: "",
        content: "",
        author: "",
        postDate: "",
        replies: 0,
        views: 0,
        success: false
      };
    }
  }
});

/**
 * BlackHatWorld 热度浏览工具 - 用于浏览论坛最新热门讨论
 */
export const blackhatWorldTrendingTool = createTool({
  id: "blackhatworld-trending",
  description:
    "Browse trending and recent discussions in BlackHatWorld forum by category. Use this when you want to see what's hot and popular in specific forum sections without searching for specific keywords.",
  inputSchema: z.object({
    nodeNames: z
      .array(z.string())
      .describe(`Forum node names to browse for trending content (e.g., ["Making Money", "Black Hat SEO"])\n\n${threads}`),
    daysAgo: z.number().optional().default(7).describe("Number of days ago to check for trending content (default: 7 days)"),
    order: z
      .enum(["date", "replies"])
      .optional()
      .default("date")
      .describe("Sort order - date for latest posts, replies for most active discussions"),
    pages: z.number().optional().default(1).describe("Number of pages to scrape, 1 page = 20 results"),
    minReplies: z
      .number()
      .optional()
      .default(10)
      .describe("Minimum number of replies to include (higher threshold for trending content)")
  }),
  outputSchema: z.object({
    searchUrl: z.string(),
    results: z
      .array(
        z.object({
          title: z.string(),
          url: z.string(),
          author: z.string(),
          replies: z.number(),
          views: z.number(),
          lastPost: z.string(),
          forum: z.string(),
          snippet: z.string().optional(),
          tags: z.array(z.string()).optional()
        })
      )
      .optional(),
    totalResults: z.number().optional(),
    maxPage: z.number().optional()
  }),
  execute: async ({ context }) => {
    const { pages, nodeNames, daysAgo, order, minReplies } = context;

    // 对于热度浏览，使用通配符搜索
    const keywords = "?";

    return await executeBlackhatWorldSearch({
      keywords,
      nodeNames,
      daysAgo,
      order,
      pages,
      minReplies
    });
  }
});
