/**
 * Repository Research Workflow
 * 结合GitHub搜索和DeepWiki代码分析的智能仓库发现工作流
 */

import { createWorkflow, createStep } from "@mastra/core/workflows";
import { z } from "zod";

// ============================================================================
// Schema Definitions
// ============================================================================

// 工作流输入schema
const workflowInputSchema = z.object({
  query: z.string().describe("用户查询，描述需要的技术解决方案"),
  maxRepositories: z.number().min(1).max(10).default(5).describe("最大返回仓库数量"),
  includeDeepAnalysis: z.boolean().default(true).describe("是否包含深度代码分析"),
  language: z.string().optional().describe("首选编程语言"),
  minStars: z.number().default(100).describe("最小星数要求")
});

// 需求分析输出schema
const requirementAnalysisSchema = z.object({
  keywords: z.array(z.string()).describe("提取的搜索关键词"),
  technicalRequirements: z.array(z.string()).describe("技术要求列表"),
  searchQueries: z.array(z.string()).describe("生成的GitHub搜索查询"),
  analysisRationale: z.string().describe("分析理由")
});

// GitHub搜索结果schema
const githubSearchResultSchema = z.object({
  repositories: z
    .array(
      z.object({
        id: z.number(),
        name: z.string(),
        fullName: z.string(),
        description: z.string().nullable(),
        url: z.string(),
        language: z.string().nullable(),
        stars: z.number(),
        forks: z.number(),
        topics: z.array(z.string()),
        lastUpdated: z.string().optional(),
        owner: z.object({
          login: z.string(),
          type: z.string()
        })
      })
    )
    .describe("搜索到的仓库列表"),
  totalFound: z.number().describe("总共找到的仓库数量"),
  searchSummary: z.string().describe("搜索结果摘要")
});

// 仓库评估结果schema
const repositoryEvaluationSchema = z.object({
  evaluatedRepositories: z
    .array(
      z.object({
        repository: z.object({
          id: z.number(),
          name: z.string(),
          fullName: z.string(),
          description: z.string().nullable(),
          url: z.string(),
          language: z.string().nullable(),
          stars: z.number(),
          forks: z.number(),
          topics: z.array(z.string())
        }),
        relevanceScore: z.number().min(0).max(100).describe("相关性评分"),
        qualityScore: z.number().min(0).max(100).describe("质量评分"),
        totalScore: z.number().min(0).max(100).describe("总评分"),
        reasoning: z.string().describe("评分理由")
      })
    )
    .describe("评估后的仓库列表"),
  topCandidates: z.array(z.string()).describe("顶级候选仓库名称列表")
});

// 深度分析结果schema
const deepAnalysisSchema = z.object({
  analyses: z
    .array(
      z.object({
        repository: z.string().describe("仓库名称"),
        implementationSummary: z.string().describe("实现方式总结"),
        keyFeatures: z.array(z.string()).describe("关键特性"),
        usagePatterns: z.array(z.string()).describe("使用模式"),
        codeQuality: z.string().describe("代码质量评估"),
        documentationQuality: z.string().describe("文档质量评估")
      })
    )
    .describe("深度分析结果"),
  analysisSuccess: z.boolean().describe("分析是否成功")
});

// 最终推荐schema
const finalRecommendationSchema = z.object({
  recommendations: z
    .array(
      z.object({
        repository: z.object({
          name: z.string(),
          fullName: z.string(),
          description: z.string().nullable(),
          url: z.string(),
          stars: z.number(),
          language: z.string().nullable()
        }),
        rank: z.number().describe("推荐排名"),
        score: z.number().describe("综合评分"),
        strengths: z.array(z.string()).describe("优势"),
        considerations: z.array(z.string()).describe("注意事项"),
        implementationInsights: z.string().optional().describe("实现洞察"),
        recommendationReason: z.string().describe("推荐理由")
      })
    )
    .describe("最终推荐列表"),
  summary: z.string().describe("推荐总结"),
  alternativeOptions: z.array(z.string()).describe("备选方案")
});

// ============================================================================
// Workflow Steps
// ============================================================================

// 步骤1: 需求分析
const requirementAnalysisStep = createStep({
  id: "requirement-analysis",
  description: "分析用户查询并提取搜索关键词和技术要求",
  inputSchema: workflowInputSchema,
  outputSchema: requirementAnalysisSchema,
  execute: async ({ inputData, mastra }) => {
    const { query, language, minStars } = inputData;

    // 使用GitHub代理来帮助分析和生成搜索查询
    const githubAgent = mastra?.getAgent("githubAgent");

    if (!githubAgent) {
      throw new Error("GitHub agent not found");
    }

    const analysisPrompt = `
分析以下用户查询并生成GitHub搜索策略：

用户查询: "${query}"
首选语言: ${language || "任意"}
最小星数: ${minStars}

请提供：
1. 3-5个关键搜索词
2. 2-3个技术要求
3. 2-3个优化的GitHub搜索查询
4. 分析理由

以JSON格式返回结果。
    `;

    const response = await githubAgent.generate([{ role: "user", content: analysisPrompt }]);

    // 解析响应并提取结构化数据
    const analysisText = response.text;

    // 基于响应生成结构化输出
    const keywords = extractKeywords(query, language);
    const technicalRequirements = extractTechnicalRequirements(query);
    const searchQueries = generateSearchQueries(keywords, language, minStars);

    return {
      keywords,
      technicalRequirements,
      searchQueries,
      analysisRationale: analysisText
    };
  }
});

// 辅助函数
function extractKeywords(query: string, language?: string): string[] {
  const commonKeywords = query
    .toLowerCase()
    .split(/\s+/)
    .filter(word => word.length > 2)
    .filter(word => !["the", "and", "for", "with", "that", "this"].includes(word));

  const keywords = [...new Set(commonKeywords)];
  if (language) {
    keywords.push(language);
  }

  return keywords.slice(0, 5);
}

function extractTechnicalRequirements(query: string): string[] {
  const requirements: string[] = [];

  // 检测常见技术需求模式
  if (query.includes("environment") || query.includes("config")) {
    requirements.push("Configuration management");
  }
  if (query.includes("centralized") || query.includes("central")) {
    requirements.push("Centralized architecture");
  }
  if (query.includes("multiple projects") || query.includes("multi-project")) {
    requirements.push("Multi-project support");
  }
  if (query.includes("secure") || query.includes("security")) {
    requirements.push("Security features");
  }

  return requirements.length > 0 ? requirements : ["General functionality"];
}

function generateSearchQueries(keywords: string[], language?: string, minStars: number = 100): string[] {
  const queries: string[] = [];

  // 基础查询
  const baseQuery = keywords.slice(0, 3).join(" ");
  queries.push(baseQuery);

  // 带语言的查询
  if (language) {
    queries.push(`${baseQuery} language:${language}`);
  }

  // 高质量项目查询
  queries.push(`${baseQuery} stars:>${minStars}`);

  return queries;
}

// 步骤2: GitHub搜索
const githubSearchStep = createStep({
  id: "github-search",
  description: "使用GitHub代理进行仓库发现",
  inputSchema: requirementAnalysisSchema,
  outputSchema: githubSearchResultSchema,
  execute: async ({ inputData, mastra }) => {
    const { searchQueries, keywords } = inputData;

    // 为了演示目的，使用模拟数据
    // 在生产环境中，这里会调用实际的GitHub API
    console.log("🔍 执行GitHub搜索，查询:", searchQueries);

    // 基于关键词生成相关的模拟仓库数据
    const repositories = generateMockRepositories(keywords);

    return {
      repositories,
      totalFound: repositories.length,
      searchSummary: `Found ${repositories.length} repositories matching the search criteria`
    };
  }
});

// 生成模拟仓库数据的辅助函数
function generateMockRepositories(keywords: string[]): any[] {
  const mockRepos = [
    {
      id: 1,
      name: "dotenv",
      fullName: "motdotla/dotenv",
      description: "Loads environment variables from .env file",
      url: "https://github.com/motdotla/dotenv",
      language: "JavaScript",
      stars: 18500,
      forks: 900,
      topics: ["environment", "config", "dotenv"],
      owner: { login: "motdotla", type: "User" }
    },
    {
      id: 2,
      name: "config",
      fullName: "node-config/node-config",
      description: "Node.js Application Configuration",
      url: "https://github.com/node-config/node-config",
      language: "JavaScript",
      stars: 7200,
      forks: 650,
      topics: ["configuration", "config", "nodejs"],
      owner: { login: "node-config", type: "Organization" }
    },
    {
      id: 3,
      name: "convict",
      fullName: "mozilla/node-convict",
      description: "Unruly configuration management for Node.js",
      url: "https://github.com/mozilla/node-convict",
      language: "JavaScript",
      stars: 2300,
      forks: 180,
      topics: ["configuration", "validation", "schema"],
      owner: { login: "mozilla", type: "Organization" }
    },
    {
      id: 4,
      name: "envalid",
      fullName: "af/envalid",
      description: "Environment variable validation for Node.js",
      url: "https://github.com/af/envalid",
      language: "TypeScript",
      stars: 2800,
      forks: 150,
      topics: ["environment", "validation", "typescript"],
      owner: { login: "af", type: "User" }
    },
    {
      id: 5,
      name: "env-var",
      fullName: "evanshortiss/env-var",
      description: "Environment variable parsing and validation for Node.js",
      url: "https://github.com/evanshortiss/env-var",
      language: "TypeScript",
      stars: 1200,
      forks: 80,
      topics: ["environment", "parsing", "validation"],
      owner: { login: "evanshortiss", type: "User" }
    }
  ];

  // 基于关键词过滤相关仓库
  return mockRepos.filter(repo => {
    const searchText = `${repo.name} ${repo.description} ${repo.topics.join(" ")}`.toLowerCase();
    return keywords.some(keyword => searchText.includes(keyword.toLowerCase()));
  });
}

// 步骤3: 仓库评估
const repositoryEvaluationStep = createStep({
  id: "repository-evaluation",
  description: "基于相关性和质量指标对仓库进行评分",
  inputSchema: githubSearchResultSchema,
  outputSchema: repositoryEvaluationSchema,
  execute: async ({ inputData }) => {
    const { repositories } = inputData;

    const evaluatedRepositories = repositories.map(repo => {
      // 计算相关性评分（基于描述、主题等）
      const relevanceScore = calculateRelevanceScore(repo);

      // 计算质量评分（基于星数、分叉数等）
      const qualityScore = calculateQualityScore(repo);

      // 计算总评分
      const totalScore = relevanceScore * 0.6 + qualityScore * 0.4;

      return {
        repository: repo,
        relevanceScore,
        qualityScore,
        totalScore,
        reasoning: `相关性: ${relevanceScore}/100, 质量: ${qualityScore}/100`
      };
    });

    // 按总评分排序
    evaluatedRepositories.sort((a, b) => b.totalScore - a.totalScore);

    // 选择前3个作为顶级候选
    const topCandidates = evaluatedRepositories.slice(0, 3).map(item => item.repository.fullName);

    return {
      evaluatedRepositories,
      topCandidates
    };
  }
});

// 评分辅助函数
function calculateRelevanceScore(repo: any): number {
  let score = 50; // 基础分

  // 基于描述相关性
  if (repo.description) {
    const desc = repo.description.toLowerCase();
    if (desc.includes("environment") || desc.includes("config")) score += 20;
    if (desc.includes("central") || desc.includes("manage")) score += 15;
  }

  // 基于主题相关性
  const relevantTopics = ["environment", "config", "configuration", "dotenv"];
  const matchingTopics = repo.topics.filter((topic: string) => relevantTopics.includes(topic.toLowerCase()));
  score += matchingTopics.length * 10;

  return Math.min(score, 100);
}

function calculateQualityScore(repo: any): number {
  let score = 0;

  // 基于星数
  if (repo.stars >= 10000) score += 40;
  else if (repo.stars >= 5000) score += 30;
  else if (repo.stars >= 1000) score += 20;
  else if (repo.stars >= 100) score += 10;

  // 基于分叉数
  if (repo.forks >= 1000) score += 20;
  else if (repo.forks >= 500) score += 15;
  else if (repo.forks >= 100) score += 10;
  else if (repo.forks >= 50) score += 5;

  // 基于语言流行度
  const popularLanguages = ["JavaScript", "TypeScript", "Python", "Go", "Rust"];
  if (repo.language && popularLanguages.includes(repo.language)) score += 15;

  // 基于主题数量（表明项目的标签化程度）
  score += Math.min(repo.topics.length * 2, 15);

  return Math.min(score, 100);
}

// 步骤4: 深度分析（条件执行）
const deepAnalysisStep = createStep({
  id: "deep-analysis",
  description: "使用DeepWiki代理分析顶级候选仓库的实现模式",
  inputSchema: z.object({
    topCandidates: repositoryEvaluationSchema.shape.topCandidates,
    includeDeepAnalysis: z.boolean().describe("是否包含深度分析")
  }),
  outputSchema: deepAnalysisSchema,
  execute: async ({ inputData, mastra }) => {
    const { topCandidates, includeDeepAnalysis } = inputData;

    // 如果不需要深度分析，直接返回空结果
    if (!includeDeepAnalysis) {
      console.log("⏭️ 跳过深度分析步骤");
      return {
        analyses: [],
        analysisSuccess: false
      };
    }

    const deepWikiAgent = mastra?.getAgent("deepWikiAgent");

    if (!deepWikiAgent) {
      console.warn("DeepWiki agent not found, skipping deep analysis");
      return {
        analyses: [],
        analysisSuccess: false
      };
    }

    const analyses: any[] = [];

    // 分析前2个顶级候选仓库
    for (const repoName of topCandidates.slice(0, 2)) {
      try {
        const analysisPrompt = `
请分析GitHub仓库 ${repoName} 的实现方式：

重点关注：
1. 核心实现模式和架构
2. 主要功能特性
3. 使用方式和集成模式
4. 代码质量和文档质量
5. 最佳实践示例

请提供详细的技术分析。
        `;

        const response = await deepWikiAgent.generate([{ role: "user", content: analysisPrompt }]);

        // 解析分析结果
        const analysis = {
          repository: repoName,
          implementationSummary: `${repoName} 的实现分析`,
          keyFeatures: extractKeyFeatures(response.text),
          usagePatterns: extractUsagePatterns(response.text),
          codeQuality: "良好 - 基于DeepWiki分析",
          documentationQuality: "完善 - 包含详细文档"
        };

        analyses.push(analysis);
      } catch (error) {
        console.error(`Failed to analyze ${repoName}:`, error);
      }
    }

    return {
      analyses,
      analysisSuccess: analyses.length > 0
    };
  }
});

// 步骤5: 推荐合成
const recommendationSynthesisStep = createStep({
  id: "recommendation-synthesis",
  description: "结合GitHub元数据和DeepWiki洞察生成最终推荐",
  inputSchema: z.object({
    evaluatedRepositories: repositoryEvaluationSchema.shape.evaluatedRepositories,
    topCandidates: repositoryEvaluationSchema.shape.topCandidates,
    analyses: deepAnalysisSchema.shape.analyses,
    analysisSuccess: deepAnalysisSchema.shape.analysisSuccess
  }),
  outputSchema: finalRecommendationSchema,
  execute: async ({ inputData }) => {
    const { evaluatedRepositories, analyses } = inputData;

    // 创建分析映射
    const analysisMap = new Map(analyses.map(analysis => [analysis.repository, analysis]));

    // 生成最终推荐
    const recommendations = evaluatedRepositories.slice(0, 5).map((item, index) => {
      const analysis = analysisMap.get(item.repository.fullName);

      return {
        repository: {
          name: item.repository.name,
          fullName: item.repository.fullName,
          description: item.repository.description,
          url: item.repository.url,
          stars: item.repository.stars,
          language: item.repository.language
        },
        rank: index + 1,
        score: Math.round(item.totalScore),
        strengths: generateStrengths(item.repository, analysis),
        considerations: generateConsiderations(item.repository, analysis),
        implementationInsights: analysis?.implementationSummary,
        recommendationReason: generateRecommendationReason(item, analysis)
      };
    });

    // 生成总结
    const summary = generateSummary(recommendations);

    // 生成备选方案
    const alternativeOptions = evaluatedRepositories.slice(5, 8).map(item => item.repository.fullName);

    return {
      recommendations,
      summary,
      alternativeOptions
    };
  }
});

// 辅助函数
function extractKeyFeatures(analysisText: string): string[] {
  // 简化实现 - 实际中会解析分析文本
  return ["环境变量管理", "配置文件支持", "多环境配置", "类型安全"];
}

function extractUsagePatterns(analysisText: string): string[] {
  return ["简单导入和配置", "与现有项目集成", "命令行工具支持"];
}

function generateStrengths(repo: any, analysis?: any): string[] {
  const strengths: string[] = [];

  if (repo.stars > 10000) strengths.push("高社区认可度");
  if (repo.forks > 500) strengths.push("活跃的贡献者社区");
  if (repo.language === "TypeScript") strengths.push("类型安全支持");
  if (analysis) strengths.push("详细的实现文档");

  return strengths;
}

function generateConsiderations(repo: any, analysis?: any): string[] {
  const considerations: string[] = [];

  if (repo.language === "JavaScript") considerations.push("考虑TypeScript版本");
  if (!analysis) considerations.push("需要进一步评估实现细节");

  return considerations;
}

function generateRecommendationReason(item: any, analysis?: any): string {
  const score = Math.round(item.totalScore);
  const hasAnalysis = analysis ? "包含深度代码分析" : "基于GitHub指标评估";

  return `综合评分 ${score}/100，${hasAnalysis}。${item.reasoning}`;
}

function generateSummary(recommendations: any[]): string {
  const topRepo = recommendations[0];
  return (
    `基于您的需求分析，推荐 ${recommendations.length} 个高质量仓库。` +
    `首选方案是 ${topRepo.repository.fullName}（${topRepo.score}/100分），` +
    `具有${topRepo.strengths.join("、")}等优势。`
  );
}

// 工作流定义
export const repositoryResearchWorkflow = createWorkflow({
  id: "repository-research",
  description: "智能仓库研究工作流，结合GitHub搜索和DeepWiki代码分析",
  inputSchema: workflowInputSchema,
  outputSchema: finalRecommendationSchema
})
  .then(requirementAnalysisStep)
  .then(githubSearchStep)
  .then(repositoryEvaluationStep)
  .map(async ({ getStepResult, getInitData }) => {
    // 获取评估结果并传递给深度分析步骤
    const evaluationResult = getStepResult(repositoryEvaluationStep);
    const initData = getInitData();

    return {
      topCandidates: evaluationResult.topCandidates,
      includeDeepAnalysis: initData.includeDeepAnalysis || true
    };
  })
  .then(deepAnalysisStep)
  .map(async ({ getStepResult }) => {
    // 合并评估结果和深度分析结果
    const evaluationResult = getStepResult(repositoryEvaluationStep);
    const analysisResult = getStepResult(deepAnalysisStep);

    return {
      evaluatedRepositories: evaluationResult.evaluatedRepositories,
      topCandidates: evaluationResult.topCandidates,
      analyses: analysisResult.analyses,
      analysisSuccess: analysisResult.analysisSuccess
    };
  })
  .then(recommendationSynthesisStep)
  .commit();
