import { sciraai, backupAI } from './scira.js';
import { generateText } from 'ai';

/**
 * 测试 Scira AI 包装器
 */
async function testSciraWrapper() {
  try {
    console.log("=== 测试 Scira AI 包装器 ===");
    
    // 测试非流式响应
    console.log("1. 测试非流式响应...");
    const response = await generateText({
      model: sciraai("gpt-4o-mini"), // 使用 OpenAI 模型名，会自动映射到 scira-anthropic
      prompt: "Hello, how are you? Please respond in Chinese."
    });

    console.log("Response text:", response.text);
    console.log("Usage:", response.usage);
    console.log("Model used:", response.response.modelId);
    
    // 测试不同的模型映射
    console.log("\n2. 测试模型映射...");
    const models = ["gpt-4o", "gpt-4", "claude-3-sonnet"];
    
    for (const modelName of models) {
      console.log(`\n测试模型: ${modelName}`);
      const testResponse = await generateText({
        model: scira<PERSON>(modelName),
        prompt: "Say hello in one sentence."
      });
      console.log(`Response: ${testResponse.text.substring(0, 100)}...`);
      console.log(`Mapped to: ${testResponse.response.modelId}`);
    }
    
    console.log("\n=== 所有测试完成 ===");
    
  } catch (error) {
    console.error("测试失败:", error.message);
    console.error("错误详情:", error);
  }
}

// 运行测试
testSciraWrapper();
