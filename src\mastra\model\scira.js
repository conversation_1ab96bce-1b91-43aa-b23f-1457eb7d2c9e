import { ProxyAgent } from "undici";
import { createOpenAI } from "@ai-sdk/openai";
import { generateText } from "ai";

const proxyAgent = new ProxyAgent("http://127.0.0.1:7890");

/**
 * Scira API 包装器 - 最简单版本
 * 基于 zaidmukaddam/scira 和 iidamie/deepseek2api 的研究
 */

class SciraAPI {
  constructor(options = {}) {
    this.baseUrl = "https://scira.ai/api/search";
    this.sessionToken = options.sessionToken || null;
    this.timezone = options.timezone || "Asia/Shanghai";
    this.visibilityType = options.visibilityType || "private";
    this.customInstructions = options.customInstructions !== false;

    // 支持的模型列表（基于研究）
    this.models = {
      "scira-default": "scira-default",
      "scira-vision": "scira-vision",
      "scira-grok-4": "scira-grok-4",
      "scira-anthropic": "scira-anthropic",
      "scira-o3": "scira-o3",
      "scira-llama-4": "scira-llama-4"
    };

    // 搜索组（基于研究）
    this.groups = {
      web: "web",
      academic: "academic",
      reddit: "reddit",
      analysis: "analysis",
      crypto: "crypto",
      memory: "memory",
      extreme: "extreme",
      youtube: "youtube",
      chat: "chat"
    };

    // 会话管理
    this.sessions = new Map();
  }

  /**
   * 生成会话ID（类似 deepseek2api 的 session 管理）
   */
  generateSessionId() {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
      const r = (Math.random() * 16) | 0;
      const v = c == "x" ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  }

  /**
   * 获取或创建会话
   */
  getSession(sessionId = null) {
    if (!sessionId) {
      sessionId = this.generateSessionId();
    }

    if (!this.sessions.has(sessionId)) {
      this.sessions.set(sessionId, {
        id: sessionId,
        messages: [],
        createdAt: new Date().toISOString()
      });
    }

    return this.sessions.get(sessionId);
  }

  /**
   * 处理消息历史（参考 deepseek2api 的 messages_prepare）
   */
  prepareMessages(messages, sessionId = null) {
    const session = this.getSession(sessionId);

    // 合并新消息到会话历史
    const allMessages = [...session.messages, ...messages];

    // 更新会话历史
    session.messages = allMessages;

    // 处理消息格式
    return allMessages.map(msg => ({
      id: msg.id || this.generateMessageId(),
      createdAt: msg.createdAt || new Date().toISOString(),
      role: msg.role || "user",
      content: msg.content || "",
      parts: msg.parts || [{ type: "text", text: msg.content || "" }]
    }));
  }

  /**
   * 生成消息ID
   */
  generateMessageId() {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  /**
   * 构建请求头
   */
  buildHeaders(sessionId = null) {
    const headers = {
      accept: "*/*",
      "accept-language": "zh-CN,zh;q=0.9",
      "content-type": "application/json",
      priority: "u=1, i",
      "sec-ch-ua": '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-platform": '"macOS"',
      "sec-fetch-dest": "empty",
      "sec-fetch-mode": "cors",
      "sec-fetch-site": "same-origin",
      Referer: sessionId ? `https://scira.ai/search/${sessionId}` : "https://scira.ai/",
      "Referrer-Policy": "strict-origin-when-cross-origin"
    };

    // 使用完整的 cookie（参考 f.js）
    if (this.sessionToken) {
      headers.cookie = `better-auth.session_token=${this.sessionToken}; ph_phc_iy7AsVPrSER6rEqnru1DlRr1rIy0GVYRilMbTIGUYrK_posthog=%7B%22distinct_id%22%3A%220197dd5d-5c5d-76eb-98e5-2ec206fc5a9f%22%2C%22%24sesid%22%3A%5B1753795866997%2C%2201985660-c07f-73f3-95c1-9357484d5b02%22%2C1753795838078%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22https%3A%2F%2Fscira.ai%2F%22%7D%7D`;
    }

    return headers;
  }

  /**
   * 发送搜索请求
   */
  async search(options = {}) {
    const { messages = [], model = "scira-anthropic", group = "reddit", sessionId = null, stream = false } = options;

    // 验证模型和组
    if (!this.models[model]) {
      throw new Error(`不支持的模型: ${model}`);
    }
    if (!this.groups[group]) {
      throw new Error(`不支持的搜索组: ${group}`);
    }

    // 获取会话
    const session = this.getSession(sessionId);

    // 准备消息
    const preparedMessages = this.prepareMessages(messages, session.id);

    // 构建请求体
    const requestBody = {
      id: session.id,
      messages: preparedMessages,
      model: model,
      group: group,
      timezone: this.timezone,
      selectedVisibilityType: this.visibilityType,
      isCustomInstructionsEnabled: this.customInstructions
    };

    try {
      const response = await fetch(this.baseUrl, {
        dispatcher: proxyAgent,
        method: "POST",
        headers: this.buildHeaders(session.id),
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      if (stream) {
        return this.handleStreamResponse(response, session.id);
      } else {
        const result = await response.text();
        return {
          sessionId: session.id,
          response: result,
          messages: preparedMessages
        };
      }
    } catch (error) {
      throw new Error(`请求失败: ${error.message}`);
    }
  }

  /**
   * 处理流式响应
   */
  async handleStreamResponse(response, sessionId) {
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let result = "";

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        result += chunk;
      }
    } finally {
      reader.releaseLock();
    }

    return {
      sessionId: sessionId,
      response: result,
      stream: true
    };
  }

  /**
   * 转换为 OpenAI API 格式的流式响应
   */
  async *convertToOpenAIStream(response, model = "scira-anthropic") {
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    const completionId = `chatcmpl-${this.generateMessageId()}`;
    const created = Math.floor(Date.now() / 1000);
    let buffer = "";
    let isFirstChunk = true;

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");
        buffer = lines.pop() || ""; // 保留最后一个不完整的行

        for (const line of lines) {
          if (!line.trim()) continue;

          // 解析 Scira 的响应格式
          if (line.startsWith('0:"')) {
            // 提取文本内容
            const content = line.slice(3, -1); // 移除 0:" 和末尾的 "

            const chunk = {
              id: completionId,
              object: "chat.completion.chunk",
              created: created,
              model: model,
              choices: [
                {
                  delta: isFirstChunk ? { role: "assistant", content: content } : { content: content },
                  index: 0
                }
              ]
            };

            yield `data: ${JSON.stringify(chunk)}\n\n`;
            isFirstChunk = false;
          } else if (line.startsWith("e:")) {
            // 处理结束信息
            try {
              const endData = JSON.parse(line.slice(2));
              const finishChunk = {
                id: completionId,
                object: "chat.completion.chunk",
                created: created,
                model: model,
                choices: [
                  {
                    delta: {},
                    index: 0,
                    finish_reason: "stop"
                  }
                ],
                usage: {
                  prompt_tokens: endData.usage?.promptTokens || 0,
                  completion_tokens: endData.usage?.completionTokens || 0,
                  total_tokens: (endData.usage?.promptTokens || 0) + (endData.usage?.completionTokens || 0)
                }
              };

              yield `data: ${JSON.stringify(finishChunk)}\n\n`;
              yield `data: [DONE]\n\n`;
            } catch (e) {
              console.error("解析结束数据失败:", e);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  /**
   * 简化的聊天接口
   */
  async chat(message, options = {}) {
    const messages = [
      {
        role: "user",
        content: message
      }
    ];

    return await this.search({
      messages,
      ...options
    });
  }

  /**
   * OpenAI API 兼容的聊天完成接口
   */
  async chatCompletions(options = {}) {
    const { messages = [], model = "scira-anthropic", stream = false, ...otherOptions } = options;

    if (stream) {
      // 返回流式响应生成器
      const response = await fetch(this.baseUrl, {
        dispatcher: proxyAgent,
        method: "POST",
        headers: this.buildHeaders(),
        body: JSON.stringify({
          id: this.generateSessionId(),
          messages: this.prepareMessages(messages),
          model: model,
          group: otherOptions.group || "reddit",
          timezone: this.timezone,
          selectedVisibilityType: this.visibilityType,
          isCustomInstructionsEnabled: this.customInstructions
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return this.convertToOpenAIStream(response, model);
    } else {
      // 非流式响应
      const result = await this.search({
        messages,
        model,
        stream: false,
        ...otherOptions
      });

      // 转换为 OpenAI API 格式
      const completionId = `chatcmpl-${this.generateMessageId()}`;
      const created = Math.floor(Date.now() / 1000);

      // 从原始响应中提取文本内容
      let content = "";
      let usage = { prompt_tokens: 0, completion_tokens: 0, total_tokens: 0 };

      const lines = result.response.split("\n");
      for (const line of lines) {
        if (line.startsWith('0:"')) {
          content += line.slice(3, -1);
        } else if (line.startsWith("e:")) {
          try {
            const endData = JSON.parse(line.slice(2));
            usage = {
              prompt_tokens: endData.usage?.promptTokens || 0,
              completion_tokens: endData.usage?.completionTokens || 0,
              total_tokens: (endData.usage?.promptTokens || 0) + (endData.usage?.completionTokens || 0)
            };
          } catch (e) {
            console.error("解析使用统计失败:", e);
          }
        }
      }

      return {
        id: completionId,
        object: "chat.completion",
        created: created,
        model: model,
        choices: [
          {
            index: 0,
            message: {
              role: "assistant",
              content: content
            },
            finish_reason: "stop"
          }
        ],
        usage: usage
      };
    }
  }

  /**
   * 获取会话历史
   */
  getSessionHistory(sessionId) {
    const session = this.sessions.get(sessionId);
    return session ? session.messages : [];
  }

  /**
   * 清除会话
   */
  clearSession(sessionId) {
    return this.sessions.delete(sessionId);
  }

  /**
   * 列出所有会话
   */
  listSessions() {
    return Array.from(this.sessions.keys());
  }
}
const scira = new SciraAPI({
  sessionToken: "NO53goAlTmJ9L75JatTqNKOIPwHQwPiQ.%2FNoQsq0AwhNkiLSd2W7qYo%2Bnv9qYwbhJ4%2F13HEiDFJc%3D", // 可选
  timezone: "Asia/Shanghai"
});
// 使用示例
async function example() {
  // 初始化 API
  const scira = new SciraAPI({
    sessionToken: "NO53goAlTmJ9L75JatTqNKOIPwHQwPiQ.%2FNoQsq0AwhNkiLSd2W7qYo%2Bnv9qYwbhJ4%2F13HEiDFJc%3D", // 可选
    timezone: "Asia/Shanghai"
  });

  try {
    // 简单聊天
    const result = await scira.chat("你好，请介绍一下自己", {
      model: "scira-anthropic",
      group: "reddit"
    });

    console.log("响应:", result.response);
    console.log("会话ID:", result.sessionId);

    // 继续对话（使用相同会话ID）
    const followUp = await scira.search({
      messages: [
        {
          role: "user",
          content: "请详细说明你的功能"
        }
      ],
      sessionId: result.sessionId,
      model: "scira-anthropic",
      group: "web"
    });

    console.log("后续响应:", followUp.response);
  } catch (error) {
    console.error("错误:", error.message);
  }
}

// 导出
// if (typeof module !== 'undefined' && module.exports) {
//     module.exports = SciraAPI;
// } else if (typeof window !== 'undefined') {
//     window.SciraAPI = SciraAPI;
// }

// 测试 OpenAI API 格式
async function testOpenAIFormat() {
  const scira = new SciraAPI({
    sessionToken: "NO53goAlTmJ9L75JatTqNKOIPwHQwPiQ.%2FNoQsq0AwhNkiLSd2W7qYo%2Bnv9qYwbhJ4%2F13HEiDFJc%3D"
  });

  try {
    // 测试非流式 OpenAI API 格式
    console.log("=== 测试非流式 OpenAI API 格式 ===");
    const result = await scira.chatCompletions({
      messages: [{ role: "user", content: "你好，请简单介绍一下自己" }],
      model: "scira-anthropic",
      group: "chat"
    });
    console.log(JSON.stringify(result, null, 2));

    // 测试流式 OpenAI API 格式
    console.log("\n=== 测试流式 OpenAI API 格式 ===");
    const streamResult = await scira.chatCompletions({
      messages: [{ role: "user", content: "简单说一下今天天气" }],
      model: "scira-anthropic",
      group: "chat",
      stream: true
    });

    for await (const chunk of streamResult) {
      process.stdout.write(chunk);
    }
  } catch (error) {
    console.error("错误:", error.message);
  }
}

// testOpenAIFormat();

/**
 * 创建一个包装器，将 scira.chatCompletions 的响应转换为 fetch 兼容的 Response 对象
 */
function createSciraFetchWrapper(sciraInstance) {
  // OpenAI 模型名称到 Scira 模型名称的映射
  const modelMapping = {
    "gpt-4o-mini": "scira-anthropic",
    "gpt-4o": "scira-anthropic",
    "gpt-4": "scira-anthropic",
    "gpt-3.5-turbo": "scira-anthropic",
    "claude-3-sonnet": "scira-anthropic",
    "claude-3-haiku": "scira-anthropic",
    "claude-3-opus": "scira-anthropic",
    "claude-3.5-sonnet": "scira-anthropic",
    // 默认映射
    default: "scira-anthropic"
  };

  return async (url, options) => {
    try {
      console.log("Custom fetch called:", url, options?.method);

      // 解析请求参数
      const requestBody = JSON.parse(options.body);
      const { messages, model: originalModel = "scira-anthropic", stream = false } = requestBody;

      // 映射模型名称
      const sciraModel = modelMapping[originalModel] || modelMapping["default"];

      if (stream) {
        // 处理流式响应
        const streamGenerator = await sciraInstance.chatCompletions({
          messages: messages,
          model: sciraModel,
          group: "chat",
          stream: true
        });

        // 创建一个 ReadableStream 来包装异步生成器
        const readableStream = new ReadableStream({
          async start(controller) {
            try {
              for await (const chunk of streamGenerator) {
                controller.enqueue(new TextEncoder().encode(chunk));
              }
              controller.close();
            } catch (error) {
              controller.error(error);
            }
          }
        });

        // 返回一个 Response 对象，包含流式数据
        return new Response(readableStream, {
          status: 200,
          statusText: "OK",
          headers: {
            "Content-Type": "text/event-stream",
            "Cache-Control": "no-cache",
            Connection: "keep-alive"
          }
        });
      } else {
        // 处理非流式响应
        const result = await sciraInstance.chatCompletions({
          messages: messages,
          model: sciraModel,
          group: "chat",
          stream: false
        });

        console.log("Scira response:", result);

        // 返回一个 Response 对象，包含 JSON 数据
        return new Response(JSON.stringify(result), {
          status: 200,
          statusText: "OK",
          headers: {
            "Content-Type": "application/json"
          }
        });
      }
    } catch (error) {
      console.error("Custom fetch error:", error);

      // 返回错误响应
      return new Response(
        JSON.stringify({
          error: {
            message: error.message,
            type: "scira_api_error"
          }
        }),
        {
          status: 500,
          statusText: "Internal Server Error",
          headers: {
            "Content-Type": "application/json"
          }
        }
      );
    }
  };
}

// 创建 Scira AI 提供者实例
export const sciraai = createOpenAI({
  baseURL: "https://scira.ai/api/search",
  apiKey: "NO53goAlTmJ9L75JatTqNKOIPwHQwPiQ.%2FNoQsq0AwhNkiLSd2W7qYo%2Bnv9qYwbhJ4%2F13HEiDFJc%3D",
  compatibility: "compatible",
  fetch: createSciraFetchWrapper(scira)
});



/**
 * 测试 Scira AI 包装器
 */
async function testSciraWrapper() {
  try {
    console.log("=== 测试 Scira AI 包装器 ===");

    // 测试非流式响应
    console.log("1. 测试非流式响应...");
    const response = await generateText({
      model: sciraai("gpt-4o-mini"), // 使用 OpenAI 模型名，会自动映射到 scira-anthropic
      prompt: "Hello, how are you? Please respond in Chinese."
    });

    console.log("Response text:", response.text);
    console.log("Usage:", response.usage);
    console.log("Model used:", response.response.modelId);

    // 测试流式响应
    console.log("\n2. 测试流式响应...");
    const { textStream } = await generateText({
      model: sciraai("gpt-4o-mini"),
      prompt: "Tell me a very short story about AI in Chinese",
      experimental_streamText: true
    });

    console.log("Stream output:");
    for await (const chunk of textStream) {
      process.stdout.write(chunk);
    }
    console.log("\n=== 流式响应完成 ===");
  } catch (error) {
    console.error("测试失败:", error.message);
  }
}

// 取消注释下面这行来运行测试
// testSciraWrapper();

// 导出 SciraAPI 类以供其他模块使用
export { SciraAPI };
