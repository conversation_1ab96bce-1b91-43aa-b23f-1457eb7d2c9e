#!/usr/bin/env node
import { MCPServer } from "@mastra/mcp";
import { blackhatWorldScrapeTool, blackhatWorldSearchTool, blackhatWorldTrendingTool } from "./tools/blacthatworld/index";
import { githubSearchTool, githubBatchSearchTool } from "./tools/github/index";
import { deepWikiSearchTool, deepWikiBatchSearchTool } from "./tools/deepwiki/index";

const server = new MCPServer({
  name: "blackhatworld-mcp-server",
  version: "1.0.0",
  tools: {
    blackhatWorldScrapeTool,
    blackhatWorldSearchTool,
    blackhatWorldTrendingTool,
    githubSearchTool,
    githubBatchSearchTool,
    deepWikiSearchTool,
    deepWikiBatchSearchTool
  }
});

server.startStdio().catch(error => {
  console.error("Error running MCP server:", error);
  process.exit(1);
});
