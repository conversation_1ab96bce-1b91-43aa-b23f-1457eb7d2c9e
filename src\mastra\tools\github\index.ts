/**
 * GitHub Tools for Mastra
 * 专注于高质量仓库搜索，自动过滤优质项目（>100星，活跃维护）
 * 简化接口，移除过度复杂的参数，内置最佳实践过滤条件
 */

import { z } from "zod";
import { GitHubClient, GitHubSearchParams } from "./client.js";
import { GitHubParser } from "./parser.js";
import { createTool } from "@mastra/core/tools";

// ============================================================================
// Tool Definitions - Simple Design
// ============================================================================

// Single search schema - 增强的高质量搜索
const singleSearchSchema = z.object({
  query: z.string().describe("Search query. Examples: 'react hooks', 'authentication middleware', 'machine learning python'"),
  searchType: z
    .enum(["repositories", "code"])
    .describe("Search type: 'repositories' for high-quality GitHub repos, 'code' for code examples"),
  language: z.string().optional().describe("Programming language filter (e.g., 'javascript', 'python', 'typescript')"),
  stars: z.string().optional().describe("Star count filter (e.g., '>100', '100..500')"),
  createdDaysAgo: z.number().optional().describe("Created date filter (e.g., 3, 7, 15, 30, 60, 90, 180, 365)"),
  pushedDaysAgo: z.number().optional().describe("Pushed date filter (e.g., 3, 7, 15, 30, 60, 90, 180, 365)"),
  limit: z.number().min(1).max(50).default(20).describe("Number of results to return (max 50, default 20)"),
  // 新增高级搜索参数
  in: z
    .array(z.enum(["description", "readme", "name", "topics"]))
    .optional()
    .describe("Search scope: description, readme, name, topics. Example: ['description', 'readme']"),
  exactMatch: z.boolean().optional().describe("Use exact phrase matching with quotes for precise results"),
  excludeTerms: z
    .array(z.string())
    .optional()
    .describe("Terms to exclude from search results. Example: ['deprecated', 'archived']"),
  orTerms: z
    .array(z.string())
    .optional()
    .describe("Terms to search with OR logic. Example: ['tailwindcss', 'shadcn', 'framer-motion']")
});

// Batch search schema
const batchSearchSchema = z.object({
  queries: z
    .array(
      z.object({
        query: z.string().describe("Search query"),
        searchType: z.enum(["repositories", "code"]).describe("Search type"),
        language: z.string().optional().describe("Programming language filter"),
        stars: z.string().optional().describe("Star count filter (e.g., '>100', '100..500')"),
        createdDaysAgo: z.number().optional().describe("Created date filter (e.g., 3, 7, 15, 30, 60, 90, 180, 365)"),
        pushedDaysAgo: z.number().optional().describe("Pushed date filter (e.g., 3, 7, 15, 30, 60, 90, 180, 365)"),
        limit: z.number().min(1).max(50).default(20).optional(),
        // 新增高级搜索参数
        in: z.array(z.enum(["description", "readme", "name", "topics"])).optional(),
        exactMatch: z.boolean().optional(),
        excludeTerms: z.array(z.string()).optional(),
        orTerms: z.array(z.string()).optional()
      })
    )
    .min(1)
    .max(5)
    .describe("Array of search queries (max 5 for performance)"),
  concurrent: z.boolean().default(true).describe("Whether to run queries concurrently for faster results")
});

// Output schemas
const searchResultSchema = z.object({
  success: z.boolean().describe("Whether the search was successful"),
  query: z.string().describe("The search query that was used"),
  searchType: z.string().describe("The type of search performed"),
  totalCount: z.number().describe("Total number of results found"),
  resultCount: z.number().describe("Number of results returned"),
  summary: z.string().describe("Summary of search results"),
  content: z.string().describe("Formatted markdown content"),
  repositories: z
    .array(
      z.object({
        id: z.number(),
        name: z.string(),
        fullName: z.string(),
        description: z.string().nullable(),
        url: z.string(),
        language: z.string().nullable(),
        stars: z.number(),
        forks: z.number(),
        topics: z.array(z.string()),
        owner: z.object({
          login: z.string(),
          type: z.string()
        })
      })
    )
    .optional()
    .describe("Repository results (if searchType is 'repositories')"),
  codeResults: z
    .array(
      z.object({
        fileName: z.string(),
        filePath: z.string(),
        htmlUrl: z.string(),
        repository: z.object({
          name: z.string(),
          fullName: z.string(),
          url: z.string(),
          stars: z.number(),
          language: z.string().nullable()
        }),
        score: z.number()
      })
    )
    .optional()
    .describe("Code results (if searchType is 'code')"),
  executionTime: z.number().describe("Execution time in milliseconds"),
  error: z.string().optional().describe("Error message if search failed")
});

const batchSearchOutputSchema = z.object({
  results: z.array(searchResultSchema).describe("Array of search results"),
  totalExecutionTime: z.number().describe("Total execution time in milliseconds"),
  successCount: z.number().describe("Number of successful searches"),
  failureCount: z.number().describe("Number of failed searches")
});

// ============================================================================
// Helper Functions
// ============================================================================

// Execute single search with enhanced high-quality defaults
async function executeSingleSearch(
  query: string,
  searchType: "repositories" | "code",
  options: any = {}
): Promise<z.infer<typeof searchResultSchema>> {
  const client = new GitHubClient();
  const parser = new GitHubParser();
  const startTime = Date.now();

  try {
    // 构建增强的高质量搜索参数
    const searchParams: GitHubSearchParams = {
      query,
      searchType,
      language: options.language,
      createdDaysAgo: options.createdDaysAgo || 365, // 默认1年内创建（相对较新的项目）
      pushedDaysAgo: options.pushedDaysAgo || 365, // 默认1年内有推送（最近有维护）
      per_page: options.limit || 20,
      // 高质量过滤参数
      stars: options.stars || ">100", // 用户指定或默认至少100星
      sort: "stars", // 按星数排序
      order: "desc",
      // 新增高级搜索参数
      in: options.in || (searchType === "repositories" ? ["description", "readme"] : undefined),
      exactMatch: options.exactMatch,
      excludeTerms: options.excludeTerms,
      orTerms: options.orTerms
    };

    // 对于仓库搜索，添加额外的质量过滤
    if (searchType === "repositories") {
      searchParams.is = ["public"]; // 只搜索公开仓库
      // 如果没有指定搜索范围，默认在description和readme中搜索
      if (!searchParams.in) {
        searchParams.in = ["description", "readme"];
      }
    }

    const response = await client.search(searchParams);
    const parsed = parser.parseResponse(response, searchType);
    const executionTime = Date.now() - startTime;

    return {
      success: true,
      query,
      searchType,
      totalCount: parsed.totalCount,
      resultCount: searchType === "repositories" ? parsed.repositories?.length || 0 : parsed.codeResults?.length || 0,
      summary: parsed.summary,
      content: parsed.markdown,
      repositories: parsed.repositories,
      codeResults: parsed.codeResults,
      executionTime
    };
  } catch (error) {
    const executionTime = Date.now() - startTime;
    console.error(`❌ GitHub search failed for ${searchType}:`, error);

    return {
      success: false,
      query,
      searchType,
      totalCount: 0,
      resultCount: 0,
      summary: "Search failed",
      content: "",
      executionTime,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

// ============================================================================
// Tool Implementations
// ============================================================================

// Single search tool
export const githubSearchTool = createTool({
  id: "github-search",
  description:
    "Enhanced GitHub search with advanced syntax support. Features: smart keyword filtering, in:description/readme targeting, exact phrase matching, and quality filtering (>100 stars, actively maintained). Automatically optimizes queries for better results.",
  inputSchema: singleSearchSchema,
  outputSchema: searchResultSchema,
  execute: async ({ context }) => {
    const { query, searchType, limit, in: inFields, exactMatch, excludeTerms, orTerms, ...options } = context;
    return await executeSingleSearch(query, searchType, {
      ...options,
      limit,
      in: inFields,
      exactMatch,
      excludeTerms,
      orTerms
    });
  }
});

// Batch search tool
export const githubBatchSearchTool = createTool({
  id: "github-batch-search",
  description:
    "Search multiple high-quality GitHub repositories or code patterns concurrently. Maximum 5 queries per batch for efficient comparison.",
  inputSchema: batchSearchSchema,
  outputSchema: batchSearchOutputSchema,
  execute: async ({ context }) => {
    const { queries, concurrent } = context;
    const startTime = Date.now();

    console.log(`🚀 Starting ${concurrent ? "concurrent" : "sequential"} GitHub batch search for ${queries.length} queries`);

    let results: z.infer<typeof searchResultSchema>[];

    if (concurrent) {
      // Execute all queries concurrently
      results = await Promise.all(
        queries.map(({ query, searchType, limit, in: inFields, exactMatch, excludeTerms, orTerms, ...options }) =>
          executeSingleSearch(query, searchType, {
            ...options,
            limit,
            in: inFields,
            exactMatch,
            excludeTerms,
            orTerms
          })
        )
      );
    } else {
      // Execute queries sequentially
      results = [];
      for (const { query, searchType, limit, in: inFields, exactMatch, excludeTerms, orTerms, ...options } of queries) {
        const result = await executeSingleSearch(query, searchType, {
          ...options,
          limit,
          in: inFields,
          exactMatch,
          excludeTerms,
          orTerms
        });
        results.push(result);
      }
    }

    const totalExecutionTime = Date.now() - startTime;
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;

    console.log(`✅ GitHub batch search completed: ${successCount} success, ${failureCount} failures in ${totalExecutionTime}ms`);

    return {
      results,
      totalExecutionTime,
      successCount,
      failureCount
    };
  }
});

executeSingleSearch("account rotation manager", "repositories").then(console.log);
